<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - رقم الهاتف | نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="registration-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <div class="registration-container">
        <!-- Header -->
        <header class="registration-header">
            <div class="logo-container">
                <div class="cross-icon">✝</div>
                <h1 class="app-title">نور الحياة</h1>
                <p class="app-subtitle">تسجيل عضو جديد</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step-line completed"></div>
                <div class="step completed">2</div>
                <div class="step-line completed"></div>
                <div class="step completed">3</div>
                <div class="step-line completed"></div>
                <div class="step completed">4</div>
                <div class="step-line completed"></div>
                <div class="step active">5</div>
            </div>
        </header>

        <!-- Registration Content -->
        <main class="registration-content">
            <h2 class="step-title">رقم الهاتف 📱</h2>
            <p class="step-subtitle">الخطوة الأخيرة - أدخل رقم هاتفك للتواصل</p>

            <form id="phoneForm">
                <div class="form-group">
                    <label for="countryCode" class="form-label">كود البلد *</label>
                    <select id="countryCode" name="countryCode" class="form-input">
                        <option value="+20">مصر (+20)</option>
                        <option value="+966">السعودية (+966)</option>
                        <option value="+971">الإمارات (+971)</option>
                        <option value="+965">الكويت (+965)</option>
                        <option value="+974">قطر (+974)</option>
                        <option value="+973">البحرين (+973)</option>
                        <option value="+968">عُمان (+968)</option>
                        <option value="+962">الأردن (+962)</option>
                        <option value="+961">لبنان (+961)</option>
                        <option value="+963">سوريا (+963)</option>
                        <option value="+964">العراق (+964)</option>
                        <option value="+1">أمريكا/كندا (+1)</option>
                        <option value="+44">بريطانيا (+44)</option>
                        <option value="+33">فرنسا (+33)</option>
                        <option value="+49">ألمانيا (+49)</option>
                        <option value="+39">إيطاليا (+39)</option>
                        <option value="+61">أستراليا (+61)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="phoneNumber" class="form-label">رقم الهاتف *</label>
                    <input 
                        type="tel" 
                        id="phoneNumber" 
                        name="phoneNumber" 
                        class="form-input" 
                        placeholder="أدخل رقم هاتفك"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="whatsapp" class="form-label">هل هذا الرقم على واتساب؟</label>
                    <div class="gender-options" style="margin-top: 10px;">
                        <div class="gender-option" data-whatsapp="yes" onclick="selectWhatsApp('yes')" style="flex: 1;">
                            <div class="gender-icon">💬</div>
                            <div class="gender-text">نعم</div>
                        </div>
                        <div class="gender-option" data-whatsapp="no" onclick="selectWhatsApp('no')" style="flex: 1;">
                            <div class="gender-icon">📞</div>
                            <div class="gender-text">لا</div>
                        </div>
                    </div>
                </div>

                <!-- Phone validation message -->
                <div id="phoneValidation" class="note-text">
                    <strong>رقم صحيح! ✅</strong><br>
                    سنتواصل معك على هذا الرقم عند الحاجة
                </div>

                <!-- Registration summary -->
                <div id="registrationSummary" class="note-text" style="background: #f0f8ff; border-color: #2196f3; color: #1565c0; text-align: right;">
                    <strong>ملخص التسجيل:</strong><br>
                    <div id="summaryContent"></div>
                </div>

                <div style="margin-top: 40px;">
                    <button type="button" onclick="goBack()" class="skip-button">
                        ← السابق
                    </button>
                    <button type="button" id="completeBtn" class="next-button" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
                        <span>إتمام التسجيل</span>
                        <span style="margin-right: 10px;">🎉</span>
                    </button>
                </div>
            </form>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>الخطوة 5 من 5 - رقم الهاتف</p>
            <div class="footer-cross">✝</div>
        </footer>
    </div>

    <script src="script.js"></script>
    <script>
        let selectedWhatsApp = null;

        document.addEventListener('DOMContentLoaded', function() {
            initStep5();
        });

        function initStep5() {
            const countryCode = document.getElementById('countryCode');
            const phoneNumber = document.getElementById('phoneNumber');
            const completeBtn = document.getElementById('completeBtn');
            const phoneValidation = document.getElementById('phoneValidation');
            const registrationSummary = document.getElementById('registrationSummary');
            const summaryContent = document.getElementById('summaryContent');

            // Load previous data
            const savedData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            if (savedData.countryCode) countryCode.value = savedData.countryCode;
            if (savedData.phoneNumber) phoneNumber.value = savedData.phoneNumber;
            if (savedData.whatsapp) selectWhatsApp(savedData.whatsapp);

            // Show registration summary
            showSummary();

            function validatePhone() {
                const phone = phoneNumber.value.trim();
                if (phone && phone.length >= 7) {
                    phoneValidation.classList.add('show');
                    completeBtn.classList.add('enabled');
                } else {
                    phoneValidation.classList.remove('show');
                    completeBtn.classList.remove('enabled');
                }
            }

            function showSummary() {
                const data = JSON.parse(localStorage.getItem('registrationData') || '{}');
                let summary = '';
                
                if (data.firstName) {
                    const displayName = data.nickName || data.firstName;
                    summary += `<strong>الاسم:</strong> ${data.firstName} ${data.lastName || ''}<br>`;
                    if (data.nickName) summary += `<strong>الاسم المفضل:</strong> ${data.nickName}<br>`;
                }
                
                if (data.birthDate) {
                    const birth = new Date(data.birthDate);
                    const age = new Date().getFullYear() - birth.getFullYear();
                    summary += `<strong>العمر:</strong> ${age} سنة<br>`;
                }
                
                if (data.birthPlace) {
                    summary += `<strong>مكان الميلاد:</strong> ${data.birthPlace}<br>`;
                }
                
                if (data.gender) {
                    summary += `<strong>النوع:</strong> ${data.gender === 'male' ? 'ذكر' : 'أنثى'}<br>`;
                }
                
                if (data.photo) {
                    summary += `<strong>الصورة:</strong> تم رفعها ✅<br>`;
                }

                summaryContent.innerHTML = summary;
                registrationSummary.classList.add('show');
            }

            phoneNumber.addEventListener('input', validatePhone);
            countryCode.addEventListener('change', validatePhone);

            completeBtn.addEventListener('click', function() {
                if (completeBtn.classList.contains('enabled')) {
                    // Save final data
                    const currentData = JSON.parse(localStorage.getItem('registrationData') || '{}');
                    currentData.countryCode = countryCode.value;
                    currentData.phoneNumber = phoneNumber.value.trim();
                    currentData.whatsapp = selectedWhatsApp;
                    currentData.registrationDate = new Date().toISOString();
                    localStorage.setItem('registrationData', JSON.stringify(currentData));
                    
                    // Add loading animation
                    completeBtn.innerHTML = '<span>جاري إتمام التسجيل...</span><span style="margin-right: 10px;">⏳</span>';
                    
                    setTimeout(() => {
                        // Redirect to waiting page
                        window.location.href = 'waiting-page.html';
                    }, 2000);
                }
            });

            // Initial validation
            validatePhone();
        }

        function selectWhatsApp(option) {
            selectedWhatsApp = option;
            
            // Remove previous selection
            document.querySelectorAll('[data-whatsapp]').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection
            document.querySelector(`[data-whatsapp="${option}"]`).classList.add('selected');
        }

        function goBack() {
            window.location.href = 'register-step4.html';
        }

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
