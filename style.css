/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 500px;
    margin: 0 auto;
    min-height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

/* Terms Page Styles */
.terms-page {
    background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
}

.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.logo-container {
    position: relative;
    z-index: 2;
}

.cross-icon {
    font-size: 3rem;
    color: #f39c12;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px #f39c12; }
    to { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px #f39c12, 0 0 30px #f39c12; }
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.app-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

.terms-content {
    padding: 30px 20px;
}

.terms-header {
    text-align: center;
    margin-bottom: 30px;
}

.terms-header h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.decorative-line {
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #f39c12, #e74c3c);
    margin: 0 auto;
    border-radius: 2px;
}

.term-section {
    background: white;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 5px solid #f39c12;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.term-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.term-section h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2rem;
    font-weight: 600;
}

.term-section p {
    color: #555;
    line-height: 1.8;
    font-size: 0.95rem;
}

.agreement-section {
    margin-top: 40px;
    text-align: center;
}

.checkbox-container {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.custom-checkbox {
    width: 20px;
    height: 20px;
    accent-color: #f39c12;
}

.checkbox-container label {
    font-size: 1rem;
    color: #2c3e50;
    cursor: pointer;
}

.continue-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

.continue-btn:enabled:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.6);
}

.continue-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    box-shadow: none;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px;
    margin-top: auto;
}

.footer-cross {
    font-size: 1.5rem;
    color: #f39c12;
    margin-top: 10px;
}

/* Welcome Page Styles */
.welcome-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-cross {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.cross-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.cross-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.cross-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

.welcome-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 40px 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.main-cross {
    font-size: 4rem;
    color: #f39c12;
    margin-bottom: 15px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.welcome-title {
    font-size: 1.8rem;
    margin-bottom: 10px;
    font-weight: 400;
}

.app-name {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #f39c12, #e74c3c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.app-description {
    font-size: 1.1rem;
    opacity: 0.9;
}

.welcome-content {
    padding: 30px 20px;
    position: relative;
    z-index: 2;
}

.verse-container {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
}

.verse-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.bible-verse {
    margin: 20px 0;
}

.verse-text {
    font-size: 1.3rem;
    line-height: 1.8;
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 15px;
    font-family: 'Amiri', serif;
}

.verse-reference {
    font-size: 1rem;
    color: #7f8c8d;
    font-weight: 600;
}

.decorative-line-verse {
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, #f39c12, #e74c3c);
    margin: 20px auto;
    border-radius: 1px;
}

.features-preview {
    background: white;
    border-radius: 15px;
    padding: 25px 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.features-preview h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: scale(1.05);
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-text {
    font-size: 0.9rem;
    color: #2c3e50;
    font-weight: 500;
}

.login-section {
    text-align: center;
}

.login-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0 auto 15px;
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
}

.login-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(46, 204, 113, 0.6);
}

.btn-icon {
    font-size: 1.2rem;
}

.login-note {
    color: #7f8c8d;
    font-size: 1rem;
    font-style: italic;
}

.welcome-footer {
    background: rgba(44, 62, 80, 0.9);
    color: white;
    text-align: center;
    padding: 25px 20px;
    position: relative;
    z-index: 2;
}

.blessing-text p {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 15px;
    font-family: 'Amiri', serif;
}

.footer-crosses {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-crosses span {
    font-size: 1.5rem;
    color: #f39c12;
    animation: twinkle 2s ease-in-out infinite;
}

.footer-crosses span:nth-child(2) {
    animation-delay: 0.5s;
}

.footer-crosses span:nth-child(3) {
    animation-delay: 1s;
}

@keyframes twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* QR Code Styles */
.qr-code-container {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: transform 0.3s ease;
}

.qr-code-container:hover {
    transform: scale(1.1);
}

.qr-code {
    width: 60px;
    height: 60px;
    background: #2c3e50;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    text-align: center;
    cursor: pointer;
}

/* Registration Pages Styles */
.registration-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.registration-container {
    max-width: 500px;
    margin: 0 auto;
    min-height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.registration-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 30px 25px;
    text-align: center;
    position: relative;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.step.active {
    background: #f39c12;
    transform: scale(1.2);
}

.step.completed {
    background: #27ae60;
}

.step-line {
    width: 30px;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.step-line.completed {
    background: #27ae60;
}

.registration-content {
    padding: 40px 25px;
    text-align: center;
}

.step-title {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.step-subtitle {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 25px;
    text-align: right;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.form-input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background: white;
    text-align: right;
}

.form-input:focus {
    outline: none;
    border-color: #f39c12;
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

.gender-options {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 15px;
}

.gender-option {
    flex: 1;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.gender-option:hover {
    border-color: #f39c12;
    transform: translateY(-2px);
}

.gender-option.selected {
    border-color: #f39c12;
    background: #fff8e1;
}

.gender-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.gender-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.photo-upload {
    border: 2px dashed #e0e0e0;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.photo-upload:hover {
    border-color: #f39c12;
    background: #fff8e1;
}

.photo-upload.has-photo {
    border-style: solid;
    border-color: #27ae60;
}

.upload-icon {
    font-size: 3rem;
    color: #bdc3c7;
    margin-bottom: 15px;
}

.upload-text {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.skip-button {
    background: transparent;
    color: #7f8c8d;
    border: 2px solid #e0e0e0;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

.skip-button:hover {
    border-color: #bdc3c7;
    color: #2c3e50;
}

.next-button {
    background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%);
    color: white;
    border: none;
    padding: 15px 35px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
    opacity: 0.5;
    pointer-events: none;
}

.next-button.enabled {
    opacity: 1;
    pointer-events: all;
}

.next-button.enabled:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.6);
}

.note-text {
    background: #e8f5e8;
    border: 1px solid #27ae60;
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    color: #27ae60;
    font-size: 1rem;
    display: none;
}

.note-text.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Waiting Page Styles */
.waiting-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.waiting-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-element {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: floatAround 15s ease-in-out infinite;
}

.element-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.element-4 {
    top: 60%;
    right: 25%;
    animation-delay: 6s;
}

.element-5 {
    bottom: 20%;
    right: 10%;
    animation-delay: 8s;
}

.element-6 {
    top: 40%;
    left: 5%;
    animation-delay: 10s;
}

@keyframes floatAround {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.1;
    }
    25% {
        transform: translateY(-30px) translateX(20px) rotate(90deg);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-10px) translateX(-15px) rotate(180deg);
        opacity: 0.15;
    }
    75% {
        transform: translateY(-40px) translateX(10px) rotate(270deg);
        opacity: 0.1;
    }
}

.waiting-container {
    max-width: 500px;
    margin: 0 auto;
    min-height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.waiting-header {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 40px 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.waiting-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.success-animation {
    position: relative;
    z-index: 2;
    margin-bottom: 20px;
}

.checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin: 0 auto 20px;
    animation: checkmarkPulse 2s ease-in-out infinite;
}

@keyframes checkmarkPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.success-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.success-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.waiting-content {
    padding: 30px 25px;
}

.update-notice {
    text-align: center;
    margin-bottom: 40px;
    background: white;
    padding: 30px 20px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.update-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: spin 3s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.update-title {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.update-description {
    color: #7f8c8d;
    font-size: 1.1rem;
    line-height: 1.6;
}

.countdown-container {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.countdown-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.launch-date {
    background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 15px;
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 1.1rem;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.time-unit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 15px;
    border-radius: 15px;
    text-align: center;
    min-width: 70px;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.time-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.time-label {
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.time-separator {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.coming-features {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.features-title {
    text-align: center;
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 25px;
    font-weight: 600;
}

.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 20px;
}

.feature-card {
    background: #f8f9fa;
    padding: 20px 15px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: #f39c12;
    box-shadow: 0 10px 25px rgba(243, 156, 18, 0.2);
}

.feature-card .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.feature-card h4 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.feature-card p {
    font-size: 0.9rem;
    color: #7f8c8d;
    line-height: 1.4;
}

.progress-section {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.progress-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #27ae60;
}

.contact-info {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.contact-info h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    font-size: 1rem;
    color: #2c3e50;
}

.contact-icon {
    font-size: 1.2rem;
}

.waiting-footer {
    background: rgba(44, 62, 80, 0.9);
    color: white;
    text-align: center;
    padding: 30px 20px;
}

.blessing-message p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 10px;
    font-family: 'Amiri', serif;
}

.verse-ref {
    font-size: 0.9rem;
    opacity: 0.8;
    font-style: italic;
}

.footer-crosses {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.footer-crosses span {
    font-size: 1.5rem;
    color: #f39c12;
    animation: twinkle 2s ease-in-out infinite;
}

.launch-ready {
    text-align: center;
    padding: 30px 20px;
}

.launch-ready h2 {
    font-size: 2rem;
    color: #27ae60;
    margin-bottom: 20px;
}

.launch-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.launch-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.6);
}

/* Responsive Design */
@media (max-width: 600px) {
    .container, .registration-container, .waiting-container {
        max-width: 100%;
        margin: 0;
    }

    .app-title {
        font-size: 2rem;
    }

    .app-name {
        font-size: 2.5rem;
    }

    .verse-text {
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .gender-options {
        flex-direction: column;
    }

    .step-indicator {
        gap: 10px;
    }

    .step {
        width: 35px;
        height: 35px;
    }

    .step-line {
        width: 20px;
    }

    /* Waiting Page Responsive */
    .success-title {
        font-size: 2rem;
    }

    .update-title {
        font-size: 1.5rem;
    }

    .countdown-timer {
        gap: 5px;
    }

    .time-unit {
        padding: 15px 10px;
        min-width: 60px;
    }

    .time-number {
        font-size: 1.5rem;
    }

    .time-separator {
        font-size: 1.5rem;
    }

    .features-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .feature-card {
        padding: 15px 10px;
    }

    .feature-card .feature-icon {
        font-size: 2rem;
    }

    .contact-methods {
        gap: 10px;
    }

    .contact-item {
        padding: 12px;
        font-size: 0.9rem;
    }

    .floating-element {
        font-size: 1.5rem;
    }
}
