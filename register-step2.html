<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - تاريخ الميلاد | نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="registration-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <div class="registration-container">
        <!-- Header -->
        <header class="registration-header">
            <div class="logo-container">
                <div class="cross-icon">✝</div>
                <h1 class="app-title">نور الحياة</h1>
                <p class="app-subtitle">تسجيل عضو جديد</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step-line completed"></div>
                <div class="step active">2</div>
                <div class="step-line"></div>
                <div class="step">3</div>
                <div class="step-line"></div>
                <div class="step">4</div>
                <div class="step-line"></div>
                <div class="step">5</div>
            </div>
        </header>

        <!-- Registration Content -->
        <main class="registration-content">
            <h2 class="step-title">تاريخ ميلادك 🎂</h2>
            <p class="step-subtitle">هذا يساعدنا في تقديم المحتوى المناسب لك</p>

            <form id="birthdateForm">
                <div class="form-group">
                    <label for="birthDate" class="form-label">تاريخ الميلاد *</label>
                    <input 
                        type="date" 
                        id="birthDate" 
                        name="birthDate" 
                        class="form-input" 
                        required
                        max=""
                    >
                </div>

                <div class="form-group">
                    <label for="birthPlace" class="form-label">مكان الميلاد (اختياري)</label>
                    <input 
                        type="text" 
                        id="birthPlace" 
                        name="birthPlace" 
                        class="form-input" 
                        placeholder="المدينة أو البلد"
                    >
                </div>

                <!-- Age display -->
                <div id="ageDisplay" class="note-text">
                    <strong>عمرك الآن: <span id="calculatedAge"></span> سنة 🎈</strong><br>
                    بركة الرب تكون معك في كل سنين حياتك
                </div>

                <!-- Birthday wishes -->
                <div id="birthdayWish" class="note-text" style="background: #fff3cd; border-color: #f39c12; color: #856404;">
                    <strong>🎉 عيد ميلاد سعيد! 🎉</strong><br>
                    نتمنى لك سنة مليئة بالبركات والنعم
                </div>

                <div style="margin-top: 40px;">
                    <button type="button" onclick="goBack()" class="skip-button">
                        ← السابق
                    </button>
                    <button type="button" id="nextBtn" class="next-button">
                        <span>المتابعة للخطوة التالية</span>
                        <span style="margin-right: 10px;">→</span>
                    </button>
                </div>
            </form>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>الخطوة 2 من 5 - تاريخ الميلاد</p>
            <div class="footer-cross">✝</div>
        </footer>
    </div>

    <script src="script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initStep2();
        });

        function initStep2() {
            const birthDate = document.getElementById('birthDate');
            const birthPlace = document.getElementById('birthPlace');
            const nextBtn = document.getElementById('nextBtn');
            const ageDisplay = document.getElementById('ageDisplay');
            const calculatedAge = document.getElementById('calculatedAge');
            const birthdayWish = document.getElementById('birthdayWish');

            // Set max date to today
            const today = new Date().toISOString().split('T')[0];
            birthDate.max = today;

            // Load previous data
            const savedData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            if (savedData.birthDate) {
                birthDate.value = savedData.birthDate;
                calculateAge();
            }
            if (savedData.birthPlace) {
                birthPlace.value = savedData.birthPlace;
            }

            function calculateAge() {
                if (birthDate.value) {
                    const birth = new Date(birthDate.value);
                    const today = new Date();
                    let age = today.getFullYear() - birth.getFullYear();
                    const monthDiff = today.getMonth() - birth.getMonth();
                    
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                        age--;
                    }

                    calculatedAge.textContent = age;
                    ageDisplay.classList.add('show');
                    nextBtn.classList.add('enabled');

                    // Check if today is birthday
                    if (today.getMonth() === birth.getMonth() && today.getDate() === birth.getDate()) {
                        birthdayWish.classList.add('show');
                    } else {
                        birthdayWish.classList.remove('show');
                    }
                } else {
                    ageDisplay.classList.remove('show');
                    birthdayWish.classList.remove('show');
                    nextBtn.classList.remove('enabled');
                }
            }

            birthDate.addEventListener('change', calculateAge);

            nextBtn.addEventListener('click', function() {
                if (nextBtn.classList.contains('enabled')) {
                    // Save data
                    const currentData = JSON.parse(localStorage.getItem('registrationData') || '{}');
                    currentData.birthDate = birthDate.value;
                    currentData.birthPlace = birthPlace.value.trim();
                    localStorage.setItem('registrationData', JSON.stringify(currentData));
                    
                    // Add loading animation
                    nextBtn.innerHTML = '<span>جاري التحميل...</span><span style="margin-right: 10px;">⏳</span>';
                    
                    setTimeout(() => {
                        window.location.href = 'register-step3.html';
                    }, 1000);
                }
            });
        }

        function goBack() {
            window.location.href = 'register-step1.html';
        }

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
