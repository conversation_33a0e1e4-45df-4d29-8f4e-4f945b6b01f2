/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: #f8f9fa;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 15px 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo-section .logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cross-symbol {
    font-size: 2.5rem;
    color: #f39c12;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px #f39c12; }
    to { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px #f39c12, 0 0 30px #f39c12; }
}

.logo-text h1 {
    color: white;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.logo-text span {
    color: #bdc3c7;
    font-size: 0.9rem;
    font-style: italic;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover, .nav-link.active {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    transform: translateY(-2px);
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.floating-cross {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 3rem;
    animation: float 8s ease-in-out infinite;
}

.floating-dove {
    position: absolute;
    color: rgba(255, 255, 255, 0.15);
    font-size: 2rem;
    animation: fly 12s ease-in-out infinite;
}

.cross-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.cross-2 {
    top: 60%;
    right: 15%;
    animation-delay: 3s;
}

.cross-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 6s;
}

.dove-1 {
    top: 30%;
    right: 25%;
    animation-delay: 2s;
}

.dove-2 {
    bottom: 40%;
    right: 10%;
    animation-delay: 8s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(10deg); }
}

@keyframes fly {
    0%, 100% { transform: translateX(0px) translateY(0px); }
    25% { transform: translateX(20px) translateY(-15px); }
    50% { transform: translateX(-10px) translateY(-25px); }
    75% { transform: translateX(15px) translateY(-10px); }
}

.hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    max-width: 800px;
    padding: 0 20px;
}

.main-logo {
    margin-bottom: 40px;
}

.logo-circle {
    width: 150px;
    height: 150px;
    margin: 0 auto 30px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.inner-cross {
    font-size: 5rem;
    color: #f39c12;
    z-index: 3;
    position: relative;
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.logo-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.logo-ring::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: #f39c12;
    border-radius: 50%;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #fff, #f39c12);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-verse {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.verse-text {
    font-size: 1.4rem;
    line-height: 1.8;
    margin-bottom: 15px;
    font-family: 'Amiri', serif;
    font-weight: 500;
}

.verse-ref {
    font-size: 1rem;
    opacity: 0.8;
    font-style: italic;
}

/* Main Content */
.main-content {
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.terms-section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.section-title {
    font-size: 3rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 700;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 30px;
}

.decorative-line {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #f39c12, #e74c3c);
    margin: 0 auto;
    border-radius: 2px;
}

.terms-content {
    display: grid;
    gap: 30px;
    margin-bottom: 60px;
}

.term-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-left: 5px solid #f39c12;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.term-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(243, 156, 18, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.term-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-left-color: #e74c3c;
}

.term-card:hover::before {
    opacity: 1;
}

.term-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.term-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #f39c12, #e74c3c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.term-header h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.term-body {
    position: relative;
    z-index: 2;
}

.term-body p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 15px;
    font-size: 1rem;
}

.term-body ul {
    list-style: none;
    padding-right: 20px;
}

.term-body li {
    color: #666;
    margin-bottom: 8px;
    position: relative;
    padding-right: 25px;
}

.term-body li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: #27ae60;
    font-weight: bold;
}

.contact-info {
    display: grid;
    gap: 15px;
    margin-top: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.contact-icon {
    font-size: 1.2rem;
    color: #f39c12;
}

.acceptance-section {
    margin-top: 60px;
}

.acceptance-card {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 15px 40px rgba(39, 174, 96, 0.3);
}

.acceptance-card h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.acceptance-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 30px;
    opacity: 0.95;
}

.acceptance-date {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.acceptance-date p {
    margin-bottom: 10px;
    font-size: 1rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-logo {
    text-align: center;
}

.footer-cross {
    font-size: 3rem;
    color: #f39c12;
    margin-bottom: 15px;
    animation: glow 2s ease-in-out infinite alternate;
}

.footer-logo h3 {
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.footer-logo p {
    color: #bdc3c7;
    font-size: 1rem;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-section h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #f39c12;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #f39c12;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    text-align: center;
}

.footer-blessing {
    margin-bottom: 20px;
}

.footer-blessing p {
    font-size: 1.1rem;
    font-family: 'Amiri', serif;
    color: #ecf0f1;
    font-style: italic;
}

.footer-copyright p {
    color: #95a5a6;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .mobile-menu-btn {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .term-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
}
