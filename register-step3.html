<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - النوع | نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="registration-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <div class="registration-container">
        <!-- Header -->
        <header class="registration-header">
            <div class="logo-container">
                <div class="cross-icon">✝</div>
                <h1 class="app-title">نور الحياة</h1>
                <p class="app-subtitle">تسجيل عضو جديد</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step-line completed"></div>
                <div class="step completed">2</div>
                <div class="step-line completed"></div>
                <div class="step active">3</div>
                <div class="step-line"></div>
                <div class="step">4</div>
                <div class="step-line"></div>
                <div class="step">5</div>
            </div>
        </header>

        <!-- Registration Content -->
        <main class="registration-content">
            <h2 class="step-title">النوع 👤</h2>
            <p class="step-subtitle">اختر النوع المناسب لك</p>

            <form id="genderForm">
                <div class="gender-options">
                    <div class="gender-option" data-gender="male" onclick="selectGender('male')">
                        <div class="gender-icon">👨</div>
                        <div class="gender-text">ذكر</div>
                    </div>
                    
                    <div class="gender-option" data-gender="female" onclick="selectGender('female')">
                        <div class="gender-icon">👩</div>
                        <div class="gender-text">أنثى</div>
                    </div>
                </div>

                <!-- Personalized message -->
                <div id="genderNote" class="note-text">
                    <strong id="genderMessage"></strong><br>
                    <span id="genderBlessings"></span>
                </div>

                <div style="margin-top: 40px;">
                    <button type="button" onclick="goBack()" class="skip-button">
                        ← السابق
                    </button>
                    <button type="button" id="nextBtn" class="next-button">
                        <span>المتابعة للخطوة التالية</span>
                        <span style="margin-right: 10px;">→</span>
                    </button>
                </div>
            </form>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>الخطوة 3 من 5 - النوع</p>
            <div class="footer-cross">✝</div>
        </footer>
    </div>

    <script src="script.js"></script>
    <script>
        let selectedGender = null;

        document.addEventListener('DOMContentLoaded', function() {
            initStep3();
        });

        function initStep3() {
            const nextBtn = document.getElementById('nextBtn');
            
            // Load previous data
            const savedData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            if (savedData.gender) {
                selectGender(savedData.gender);
            }

            nextBtn.addEventListener('click', function() {
                if (nextBtn.classList.contains('enabled')) {
                    // Save data
                    const currentData = JSON.parse(localStorage.getItem('registrationData') || '{}');
                    currentData.gender = selectedGender;
                    localStorage.setItem('registrationData', JSON.stringify(currentData));
                    
                    // Add loading animation
                    nextBtn.innerHTML = '<span>جاري التحميل...</span><span style="margin-right: 10px;">⏳</span>';
                    
                    setTimeout(() => {
                        window.location.href = 'register-step4.html';
                    }, 1000);
                }
            });
        }

        function selectGender(gender) {
            selectedGender = gender;
            
            // Remove previous selection
            document.querySelectorAll('.gender-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selection to clicked option
            document.querySelector(`[data-gender="${gender}"]`).classList.add('selected');
            
            // Enable next button
            document.getElementById('nextBtn').classList.add('enabled');
            
            // Show personalized message
            const genderNote = document.getElementById('genderNote');
            const genderMessage = document.getElementById('genderMessage');
            const genderBlessings = document.getElementById('genderBlessings');
            
            const savedData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            const name = savedData.nickName || savedData.firstName || 'عزيزي/عزيزتي';
            
            if (gender === 'male') {
                genderMessage.textContent = `أهلاً وسهلاً أخونا ${name} 🙏`;
                genderBlessings.textContent = 'بركة الرب تكون معك وتقويك في خدمة الكنيسة';
            } else {
                genderMessage.textContent = `أهلاً وسهلاً أختنا ${name} 🙏`;
                genderBlessings.textContent = 'بركة الرب تكون معكِ وتباركِ في خدمة الكنيسة';
            }
            
            genderNote.classList.add('show');
        }

        function goBack() {
            window.location.href = 'register-step2.html';
        }

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
