<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - الاسم | نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="registration-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <div class="registration-container">
        <!-- Header -->
        <header class="registration-header">
            <div class="logo-container">
                <div class="cross-icon">✝</div>
                <h1 class="app-title">نور الحياة</h1>
                <p class="app-subtitle">تسجيل عضو جديد</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active">1</div>
                <div class="step-line"></div>
                <div class="step">2</div>
                <div class="step-line"></div>
                <div class="step">3</div>
                <div class="step-line"></div>
                <div class="step">4</div>
                <div class="step-line"></div>
                <div class="step">5</div>
            </div>
        </header>

        <!-- Registration Content -->
        <main class="registration-content">
            <h2 class="step-title">مرحباً بك! 👋</h2>
            <p class="step-subtitle">دعنا نتعرف عليك أولاً</p>

            <form id="nameForm">
                <div class="form-group">
                    <label for="firstName" class="form-label">الاسم الأول *</label>
                    <input 
                        type="text" 
                        id="firstName" 
                        name="firstName" 
                        class="form-input" 
                        placeholder="أدخل اسمك الأول"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="lastName" class="form-label">اسم العائلة *</label>
                    <input 
                        type="text" 
                        id="lastName" 
                        name="lastName" 
                        class="form-input" 
                        placeholder="أدخل اسم العائلة"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="nickName" class="form-label">الاسم المفضل (اختياري)</label>
                    <input 
                        type="text" 
                        id="nickName" 
                        name="nickName" 
                        class="form-input" 
                        placeholder="كيف تحب أن نناديك؟"
                    >
                </div>

                <!-- Note that appears when user types -->
                <div id="welcomeNote" class="note-text">
                    <strong>أهلاً وسهلاً <span id="displayName"></span>! 🌟</strong><br>
                    يسعدنا انضمامك لعائلة نور الحياة المباركة
                </div>

                <div style="margin-top: 40px;">
                    <button type="button" id="nextBtn" class="next-button">
                        <span>المتابعة للخطوة التالية</span>
                        <span style="margin-right: 10px;">→</span>
                    </button>
                </div>
            </form>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>الخطوة 1 من 5 - معلومات الاسم</p>
            <div class="footer-cross">✝</div>
        </footer>
    </div>

    <script src="script.js"></script>
    <script>
        // Initialize step 1
        document.addEventListener('DOMContentLoaded', function() {
            initStep1();
        });

        function initStep1() {
            const firstName = document.getElementById('firstName');
            const lastName = document.getElementById('lastName');
            const nickName = document.getElementById('nickName');
            const nextBtn = document.getElementById('nextBtn');
            const welcomeNote = document.getElementById('welcomeNote');
            const displayName = document.getElementById('displayName');

            function updateNextButton() {
                if (firstName.value.trim() && lastName.value.trim()) {
                    nextBtn.classList.add('enabled');
                    welcomeNote.classList.add('show');
                    
                    const name = nickName.value.trim() || firstName.value.trim();
                    displayName.textContent = name;
                } else {
                    nextBtn.classList.remove('enabled');
                    welcomeNote.classList.remove('show');
                }
            }

            firstName.addEventListener('input', updateNextButton);
            lastName.addEventListener('input', updateNextButton);
            nickName.addEventListener('input', updateNextButton);

            nextBtn.addEventListener('click', function() {
                if (nextBtn.classList.contains('enabled')) {
                    // Save data to localStorage
                    const formData = {
                        firstName: firstName.value.trim(),
                        lastName: lastName.value.trim(),
                        nickName: nickName.value.trim()
                    };
                    localStorage.setItem('registrationData', JSON.stringify(formData));
                    
                    // Add loading animation
                    nextBtn.innerHTML = '<span>جاري التحميل...</span><span style="margin-right: 10px;">⏳</span>';
                    
                    setTimeout(() => {
                        window.location.href = 'register-step2.html';
                    }, 1000);
                }
            });
        }

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
