<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - الصورة الشخصية | نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="registration-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <div class="registration-container">
        <!-- Header -->
        <header class="registration-header">
            <div class="logo-container">
                <div class="cross-icon">✝</div>
                <h1 class="app-title">نور الحياة</h1>
                <p class="app-subtitle">تسجيل عضو جديد</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step-line completed"></div>
                <div class="step completed">2</div>
                <div class="step-line completed"></div>
                <div class="step completed">3</div>
                <div class="step-line completed"></div>
                <div class="step active">4</div>
                <div class="step-line"></div>
                <div class="step">5</div>
            </div>
        </header>

        <!-- Registration Content -->
        <main class="registration-content">
            <h2 class="step-title">الصورة الشخصية 📸</h2>
            <p class="step-subtitle">أضف صورتك الشخصية (يمكن تخطي هذه الخطوة)</p>

            <form id="photoForm">
                <div class="photo-upload" id="photoUpload" onclick="triggerFileInput()">
                    <div class="upload-icon" id="uploadIcon">📷</div>
                    <div class="upload-text" id="uploadText">
                        اضغط هنا لاختيار صورة<br>
                        <small>أو اسحب الصورة هنا</small>
                    </div>
                    <img id="previewImage" style="display: none; max-width: 200px; max-height: 200px; border-radius: 10px; margin-top: 10px;">
                </div>
                
                <input type="file" id="photoInput" accept="image/*" style="display: none;">

                <!-- Photo guidelines -->
                <div class="note-text" style="display: block; background: #e3f2fd; border-color: #2196f3; color: #1565c0;">
                    <strong>نصائح للصورة الشخصية:</strong><br>
                    • اختر صورة واضحة ومناسبة<br>
                    • تأكد من ظهور وجهك بوضوح<br>
                    • يُفضل خلفية بسيطة<br>
                    • حجم الملف أقل من 5 ميجابايت
                </div>

                <!-- Upload success message -->
                <div id="uploadSuccess" class="note-text">
                    <strong>تم رفع الصورة بنجاح! ✅</strong><br>
                    صورة جميلة، بركة الرب تكون معك
                </div>

                <div style="margin-top: 40px;">
                    <button type="button" onclick="goBack()" class="skip-button">
                        ← السابق
                    </button>
                    <button type="button" onclick="skipPhoto()" class="skip-button">
                        تخطي هذه الخطوة
                    </button>
                    <button type="button" id="nextBtn" class="next-button enabled">
                        <span>المتابعة للخطوة التالية</span>
                        <span style="margin-right: 10px;">→</span>
                    </button>
                </div>
            </form>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>الخطوة 4 من 5 - الصورة الشخصية (اختيارية)</p>
            <div class="footer-cross">✝</div>
        </footer>
    </div>

    <script src="script.js"></script>
    <script>
        let hasPhoto = false;

        document.addEventListener('DOMContentLoaded', function() {
            initStep4();
        });

        function initStep4() {
            const photoInput = document.getElementById('photoInput');
            const photoUpload = document.getElementById('photoUpload');
            const previewImage = document.getElementById('previewImage');
            const uploadIcon = document.getElementById('uploadIcon');
            const uploadText = document.getElementById('uploadText');
            const uploadSuccess = document.getElementById('uploadSuccess');
            const nextBtn = document.getElementById('nextBtn');

            // Load previous data
            const savedData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            if (savedData.photo) {
                showPreview(savedData.photo);
            }

            photoInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Check file size (5MB limit)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت.');
                        return;
                    }

                    // Check file type
                    if (!file.type.startsWith('image/')) {
                        alert('يرجى اختيار ملف صورة صحيح.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        showPreview(e.target.result);
                        
                        // Save to localStorage
                        const currentData = JSON.parse(localStorage.getItem('registrationData') || '{}');
                        currentData.photo = e.target.result;
                        localStorage.setItem('registrationData', JSON.stringify(currentData));
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Drag and drop functionality
            photoUpload.addEventListener('dragover', function(e) {
                e.preventDefault();
                photoUpload.style.borderColor = '#f39c12';
                photoUpload.style.background = '#fff8e1';
            });

            photoUpload.addEventListener('dragleave', function(e) {
                e.preventDefault();
                if (!hasPhoto) {
                    photoUpload.style.borderColor = '#e0e0e0';
                    photoUpload.style.background = 'white';
                }
            });

            photoUpload.addEventListener('drop', function(e) {
                e.preventDefault();
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    photoInput.files = files;
                    photoInput.dispatchEvent(new Event('change'));
                }
            });

            function showPreview(src) {
                previewImage.src = src;
                previewImage.style.display = 'block';
                uploadIcon.style.display = 'none';
                uploadText.innerHTML = 'اضغط لتغيير الصورة';
                photoUpload.classList.add('has-photo');
                uploadSuccess.classList.add('show');
                hasPhoto = true;
            }

            nextBtn.addEventListener('click', function() {
                // Add loading animation
                nextBtn.innerHTML = '<span>جاري التحميل...</span><span style="margin-right: 10px;">⏳</span>';
                
                setTimeout(() => {
                    window.location.href = 'register-step5.html';
                }, 1000);
            });
        }

        function triggerFileInput() {
            document.getElementById('photoInput').click();
        }

        function skipPhoto() {
            // Remove photo from saved data if exists
            const currentData = JSON.parse(localStorage.getItem('registrationData') || '{}');
            delete currentData.photo;
            localStorage.setItem('registrationData', JSON.stringify(currentData));
            
            // Go to next step
            window.location.href = 'register-step5.html';
        }

        function goBack() {
            window.location.href = 'register-step3.html';
        }

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
