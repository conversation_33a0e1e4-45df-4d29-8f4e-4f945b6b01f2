// Terms and Conditions Page Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the terms page
    if (document.querySelector('.terms-page')) {
        initTermsPage();
    }
    
    // Check if we're on the welcome page
    if (document.querySelector('.welcome-page')) {
        initWelcomePage();
    }
});

function initTermsPage() {
    const agreeCheckbox = document.getElementById('agree-terms');
    const continueBtn = document.getElementById('continue-btn');
    
    if (agreeCheckbox && continueBtn) {
        // Enable/disable continue button based on checkbox
        agreeCheckbox.addEventListener('change', function() {
            continueBtn.disabled = !this.checked;
            
            if (this.checked) {
                continueBtn.style.background = 'linear-gradient(135deg, #f39c12 0%, #e74c3c 100%)';
                continueBtn.style.cursor = 'pointer';
            } else {
                continueBtn.style.background = '#bdc3c7';
                continueBtn.style.cursor = 'not-allowed';
            }
        });
        
        // Handle continue button click
        continueBtn.addEventListener('click', function() {
            if (!continueBtn.disabled) {
                // Add loading animation
                continueBtn.innerHTML = '<span>جاري التحميل...</span><span class="btn-icon">⏳</span>';
                continueBtn.disabled = true;
                
                // Simulate loading and redirect
                setTimeout(() => {
                    window.location.href = 'welcome.html';
                }, 1500);
            }
        });
    }
    
    // Add smooth scroll animation for terms sections
    const termSections = document.querySelectorAll('.term-section');
    termSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.6s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

function initWelcomePage() {
    const loginBtn = document.getElementById('loginBtn');
    
    if (loginBtn) {
        loginBtn.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            this.innerHTML = '<span class="btn-text">جاري تسجيل الدخول...</span><span class="btn-icon">⏳</span>';
            
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                // Redirect to registration
                window.location.href = 'register-step1.html';
            }, 1500);
        });
    }
    
    // Animate welcome elements on load
    animateWelcomeElements();
    
    // Add floating animation to features
    animateFeatures();
}

function animateWelcomeElements() {
    const elements = [
        '.welcome-title',
        '.app-name',
        '.app-description',
        '.verse-container',
        '.features-preview',
        '.login-section'
    ];
    
    elements.forEach((selector, index) => {
        const element = document.querySelector(selector);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.8s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 300);
        }
    });
}

function animateFeatures() {
    const featureItems = document.querySelectorAll('.feature-item');
    
    featureItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'scale(1)';
        }, 1500 + (index * 150));
    });
}

// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add CSS for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        button {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// Add scroll effects for better user experience
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.animated-bg');
    
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Add touch feedback for mobile devices
document.addEventListener('touchstart', function() {}, {passive: true});
