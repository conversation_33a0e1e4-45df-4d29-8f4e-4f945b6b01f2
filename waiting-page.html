<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انتظار التحديث - نور الحياة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="waiting-page">
    <!-- QR Code -->
    <div class="qr-code-container">
        <div class="qr-code" onclick="showQRInfo()">
            📱<br>QR
        </div>
    </div>

    <!-- Animated Background -->
    <div class="waiting-bg">
        <div class="floating-element element-1">✝</div>
        <div class="floating-element element-2">🙏</div>
        <div class="floating-element element-3">📱</div>
        <div class="floating-element element-4">✨</div>
        <div class="floating-element element-5">💫</div>
        <div class="floating-element element-6">🌟</div>
    </div>

    <div class="waiting-container">
        <!-- Header -->
        <header class="waiting-header">
            <div class="success-animation">
                <div class="checkmark">✓</div>
            </div>
            <h1 class="success-title">تم التسجيل بنجاح!</h1>
            <p class="success-subtitle">مرحباً بك في عائلة نور الحياة المباركة</p>
        </header>

        <!-- Main Content -->
        <main class="waiting-content">
            <!-- Update Notice -->
            <div class="update-notice">
                <div class="update-icon">🔄</div>
                <h2 class="update-title">جاري تحديث البرنامج</h2>
                <p class="update-description">
                    نعمل حالياً على تحديث البرنامج ليصبح متاحاً على جميع الهواتف<br>
                    مع مميزات جديدة ومحتوى روحي مبارك
                </p>
            </div>

            <!-- Countdown Timer -->
            <div class="countdown-container">
                <h3 class="countdown-title">موعد إطلاق البرنامج:</h3>
                <div class="launch-date">
                    <span class="date-text">5 يونيو 2024 - الساعة 5:00 مساءً</span>
                </div>
                
                <div class="countdown-timer">
                    <div class="time-unit">
                        <div class="time-number" id="days">00</div>
                        <div class="time-label">يوم</div>
                    </div>
                    <div class="time-separator">:</div>
                    <div class="time-unit">
                        <div class="time-number" id="hours">00</div>
                        <div class="time-label">ساعة</div>
                    </div>
                    <div class="time-separator">:</div>
                    <div class="time-unit">
                        <div class="time-number" id="minutes">00</div>
                        <div class="time-label">دقيقة</div>
                    </div>
                    <div class="time-separator">:</div>
                    <div class="time-unit">
                        <div class="time-number" id="seconds">00</div>
                        <div class="time-label">ثانية</div>
                    </div>
                </div>
            </div>

            <!-- Features Preview -->
            <div class="coming-features">
                <h3 class="features-title">ما ينتظرك في البرنامج:</h3>
                <div class="features-list">
                    <div class="feature-card">
                        <div class="feature-icon">📖</div>
                        <h4>الكتاب المقدس</h4>
                        <p>قراءة يومية مع تفسيرات</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🙏</div>
                        <h4>الصلوات</h4>
                        <p>صلوات الأجبية والطقوس</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎵</div>
                        <h4>التسابيح</h4>
                        <p>ألحان وتسابيح مقدسة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <h4>التأملات</h4>
                        <p>تأملات روحية يومية</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📅</div>
                        <h4>التقويم</h4>
                        <p>الأعياد والمناسبات</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <h4>المجتمع</h4>
                        <p>تواصل مع أعضاء الكنيسة</p>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-section">
                <h3 class="progress-title">تقدم التطوير:</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <span id="progressPercent">85</span>% مكتمل
                </div>
            </div>

            <!-- Contact Info -->
            <div class="contact-info">
                <h3>للاستفسارات والدعم:</h3>
                <div class="contact-methods">
                    <div class="contact-item">
                        <span class="contact-icon">📧</span>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-icon">📞</span>
                        <span>+20 123 456 7890</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-icon">💬</span>
                        <span>واتساب: +20 123 456 7890</span>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="waiting-footer">
            <div class="blessing-message">
                <p>"انتظروا الرب. تشددوا وليتشجع قلبكم وانتظروا الرب"</p>
                <span class="verse-ref">مزمور 27:14</span>
            </div>
            <div class="footer-crosses">
                <span>✝</span>
                <span>✝</span>
                <span>✝</span>
            </div>
        </footer>
    </div>

    <script>
        // Countdown Timer
        function updateCountdown() {
            const launchDate = new Date('2024-06-05T17:00:00').getTime();
            const now = new Date().getTime();
            const distance = launchDate - now;

            if (distance > 0) {
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                document.getElementById('days').textContent = days.toString().padStart(2, '0');
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            } else {
                // Launch time reached
                document.querySelector('.countdown-container').innerHTML = `
                    <div class="launch-ready">
                        <h2>🎉 البرنامج متاح الآن! 🎉</h2>
                        <button onclick="window.location.reload()" class="launch-btn">ادخل للبرنامج</button>
                    </div>
                `;
            }
        }

        // Progress Animation
        function animateProgress() {
            const progressFill = document.getElementById('progressFill');
            const progressPercent = document.getElementById('progressPercent');
            let progress = 0;
            const targetProgress = 85;

            const interval = setInterval(() => {
                if (progress < targetProgress) {
                    progress++;
                    progressFill.style.width = progress + '%';
                    progressPercent.textContent = progress;
                } else {
                    clearInterval(interval);
                }
            }, 50);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
            setInterval(updateCountdown, 1000);
            
            setTimeout(animateProgress, 1000);
            
            // Animate feature cards
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 2000 + (index * 200));
            });
        });

        function showQRInfo() {
            alert('رمز الاستجابة السريعة\nيمكنك مسح هذا الرمز لتحميل التطبيق على هاتفك 📱');
        }
    </script>
</body>
</html>
